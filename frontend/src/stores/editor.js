import { defineStore } from 'pinia'

export const useEditorStore = defineStore('editor', {
  state: () => ({
    statusMessage: '正在初始化...',
    connectionMode: 'packet_stream',
    cursorPosition: '0, 0',
    zoomLevel: 200,
    canUndo: false,
    canRedo: false,
    hasUnsavedChanges: false,
    stats: {
      moduleCount: 0,
      connectionCount: 0
    },
    propertyDialog: {
      visible: false,
      title: '属性编辑',
    },
    processModelDialog: {
      visible: false,
      selectedModel: '',
      node: null
    },
    connectionDialog: {
      visible: false,
      title: '连接属性',
      data: {},
      edge: null,
      availableSourcePorts: [],
      availableTargetPorts: []
    },
  }),
});


  // () => {
//   // 基础状态
//   const statusMessage = ref('正在初始化...')
//   const connectionMode = ref('packet_stream')
//   const cursorPosition = ref('0, 0')
//   const zoomLevel = ref(200)
//
//   // 编辑器状态
//   const canUndo = ref(false)
//   const canRedo = ref(false)
//   const hasUnsavedChanges = ref(false)
//
//   // 统计信息
//   const stats = reactive({
//     moduleCount: 0,
//     connectionCount: 0
//   })
//
//   // 属性对话框状态
//   const propertyDialog = reactive({
//     visible: false,
//     title: '属性编辑',
//     activeTab: 'general',
//     tabs: [
//       { id: 'general', label: '基本信息', icon: 'info' },
//       { id: 'attributes', label: '属性配置', icon: 'settings' }
//     ],
//     data: {},
//     node: null
//   })
//
//   // 进程模型对话框状态
//   const processModelDialog = reactive({
//     visible: false,
//     selectedModel: '',
//     node: null
//   })
//
//   // 连接属性对话框状态
//   const connectionDialog = reactive({
//     visible: false,
//     title: '连接属性',
//     data: {},
//     edge: null,
//     availableSourcePorts: [],
//     availableTargetPorts: []
//   })
//
//   // 进程模型列表
//   const processModels = ref([
//     {
//       name: 'aodv',
//       displayName: 'AODV路由协议',
//       description: 'Ad-hoc按需距离向量路由协议，适用于移动自组织网络'
//     },
//     {
//       name: 'arp',
//       displayName: 'ARP协议',
//       description: '地址解析协议，用于IP地址到MAC地址的映射'
//     },
//     {
//       name: 'ipv4',
//       displayName: 'IPv4协议',
//       description: 'Internet协议版本4，网络层核心协议'
//     },
//     {
//       name: 'udp',
//       displayName: 'UDP协议',
//       description: '用户数据报协议，无连接的传输层协议'
//     },
//     {
//       name: 'generic_wireless',
//       displayName: '通用无线模块',
//       description: '通用的无线通信模块，支持基本的无线传输功能'
//     }
//   ])
//
//   // Actions
//   const setStatusMessage = (message) => {
//     statusMessage.value = message
//   }
//
//   const setConnectionMode = (mode) => {
//     connectionMode.value = mode
//   }
//
//   const setCursorPosition = (position) => {
//     cursorPosition.value = position
//   }
//
//   const setZoomLevel = (level) => {
//     zoomLevel.value = level
//   }
//
//   const setCanUndo = (value) => {
//     canUndo.value = value
//   }
//
//   const setCanRedo = (value) => {
//     canRedo.value = value
//   }
//
//   const setHasUnsavedChanges = (value) => {
//     hasUnsavedChanges.value = value
//   }
//
//   const updateStats = (moduleCount, connectionCount) => {
//     stats.moduleCount = moduleCount
//     stats.connectionCount = connectionCount
//   }
//
//   const showPropertyDialog = (title, data, node) => {
//     propertyDialog.title = title
//     propertyDialog.data = { ...data }
//     propertyDialog.node = node
//     propertyDialog.activeTab = 'general'
//     propertyDialog.visible = true
//   }
//
//   const closePropertyDialog = () => {
//     propertyDialog.visible = false
//     propertyDialog.data = {}
//     propertyDialog.node = null
//   }
//
//   const showProcessModelDialog = (node) => {
//     processModelDialog.node = node
//     processModelDialog.selectedModel = node?.getData()?.model || ''
//     processModelDialog.visible = true
//   }
//
//   const closeProcessModelDialog = () => {
//     processModelDialog.visible = false
//     processModelDialog.selectedModel = ''
//     processModelDialog.node = null
//   }
//
//   const showConnectionDialog = (title, data, edge, sourcePorts, targetPorts) => {
//     connectionDialog.title = title
//     connectionDialog.data = { ...data }
//     connectionDialog.edge = edge
//     connectionDialog.availableSourcePorts = sourcePorts || []
//     connectionDialog.availableTargetPorts = targetPorts || []
//     connectionDialog.visible = true
//   }
//
//   const closeConnectionDialog = () => {
//     connectionDialog.visible = false
//     connectionDialog.data = {}
//     connectionDialog.edge = null
//     connectionDialog.availableSourcePorts = []
//     connectionDialog.availableTargetPorts = []
//   }
//
//   const getProcessModelDescription = (modelName) => {
//     const model = processModels.value.find(m => m.name === modelName)
//     return model ? model.description : '未知模型'
//   }
//
//   return {
//     // State
//     statusMessage,
//     connectionMode,
//     cursorPosition,
//     zoomLevel,
//     canUndo,
//     canRedo,
//     hasUnsavedChanges,
//     stats,
//     propertyDialog,
//     processModelDialog,
//     connectionDialog,
//     processModels,
//
//     // Actions
//     setStatusMessage,
//     setConnectionMode,
//     setCursorPosition,
//     setZoomLevel,
//     setCanUndo,
//     setCanRedo,
//     setHasUnsavedChanges,
//     updateStats,
//     showPropertyDialog,
//     closePropertyDialog,
//     showProcessModelDialog,
//     closeProcessModelDialog,
//     showConnectionDialog,
//     closeConnectionDialog,
//     getProcessModelDescription
//   }
// })

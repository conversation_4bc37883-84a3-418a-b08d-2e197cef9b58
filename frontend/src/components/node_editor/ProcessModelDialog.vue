<template>
  <q-dialog 
    v-model="editorStore.processModelDialog.visible" 
    persistent
  >
    <q-card class="process-model-dialog">
      <q-card-section class="dialog-header">
        <div class="dialog-title">
          <q-icon name="layers" />
          <span>选择进程模型</span>
        </div>
        <q-btn
          flat
          dense
          round
          icon="close"
          @click="closeProcessModelDialog"
        />
      </q-card-section>

      <q-card-section class="dialog-body">
        <q-select
          v-model="editorStore.processModelDialog.selectedModel"
          :options="processModelOptions"
          label="进程模型"
          outlined
          dense
          emit-value
          map-options
          clearable
          class="form-field"
        />

        <div class="model-description">
          <div v-if="editorStore.processModelDialog.selectedModel">
            {{ editorStore.getProcessModelDescription(editorStore.processModelDialog.selectedModel) }}
          </div>
          <div v-else>
            请选择适合的进程模型，或保持为空以使用基础模块功能。
          </div>
        </div>
      </q-card-section>

      <q-card-actions class="dialog-footer" align="right">
        <q-btn
          flat
          label="取消"
          @click="closeProcessModelDialog"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="applyProcessModel"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { useEditorStore } from 'stores/editor'

const editorStore = useEditorStore()

const processModelOptions = computed(() => {
  return editorStore.processModels.map(model => ({
    label: model.displayName,
    value: model.name
  }))
})

const closeProcessModelDialog = () => {
  editorStore.closeProcessModelDialog()
}

const applyProcessModel = () => {
  // TODO: 实现应用进程模型
  console.log('应用进程模型:', editorStore.processModelDialog.selectedModel)
  
  // 更新属性对话框中的模型信息
  if (editorStore.propertyDialog.data) {
    editorStore.propertyDialog.data.model = editorStore.processModelDialog.selectedModel
  }
  
  closeProcessModelDialog()
}
</script>

<style lang="scss" scoped>
.process-model-dialog {
  width: 400px;
  max-width: 90vw;

  .dialog-header {
    padding: 16px 24px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;

    .dialog-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .dialog-body {
    padding: 24px;

    .form-field {
      margin-bottom: 16px;
    }

    .model-description {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 4px;
      border-left: 4px solid #2196f3;
    }
  }

  .dialog-footer {
    padding: 16px 24px;
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
  }
}
</style>

<template>
  <q-dialog
    v-model="editorStore.propertyDialog.visible"
    persistent
  >
    <q-card class="attribute-dialog">
      <q-card-section class="row">
        <span>{{ editorStore.propertyDialog.title }}</span>
        <q-space />
        <q-btn
          flat
          dense
          round
          icon="close"
          @click="closePropertyDialog"
        />
      </q-card-section>

      <q-card-section class="dialog-body">
        <!-- 基础属性配置 -->
        <div class="basic-properties">
          <div class="section-title">基础配置</div>
          <div class="property-form">
            <q-input
              v-if="editorStore.propertyDialog.data.name !== undefined"
              v-model="editorStore.propertyDialog.data.name"
              label="模块名称"
              outlined
              dense
              class="form-field"
            />

            <q-input
              v-if="editorStore.propertyDialog.data.type"
              :model-value="getModuleTypeName(editorStore.propertyDialog.data.type)"
              label="模块类型"
              outlined
              dense
              readonly
              class="form-field"
            />

            <div
              v-if="ModuleConfig[editorStore.propertyDialog.data.type]?.supportProcessModel"
              class="form-field"
            >
              <q-select
                v-model="selectedProcessModel"
                :options="processModelOptions"
                label="进程模型"
                outlined
                dense
                emit-value
                map-options
                @update:model-value="onProcessModelChange"
              >
                <template v-slot:no-option>
                  <q-item>
                    <q-item-section class="text-grey">
                      暂无可用的进程模型
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>
          </div>
        </div>

        <q-separator class="section-separator" />

        <!-- 属性配置树 -->
        <div class="attribute-properties">
          <div class="section-title">属性配置</div>
          <div class="attribute-tree-container">
            <div
              v-if="!currentProcessModelAttributes || currentProcessModelAttributes.length === 0"
              class="no-attributes-message"
            >
              <q-icon name="info" size="md" class="q-mr-sm" />
              <span>请选择进程模型以配置相关属性</span>
            </div>
            <AttributeTree
              v-else
              :attributes="currentProcessModelAttributes"
              :values="editorStore.propertyDialog.data.attributeValues || {}"
              @update:values="onAttributeValuesUpdate"
            />
          </div>
        </div>
      </q-card-section>

      <q-card-actions class="dialog-footer" align="right">
        <q-btn
          flat
          label="取消"
          @click="closePropertyDialog"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="applyAndCloseProperties"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useEditorStore } from 'stores/editor'
import {getModuleTypeName, ModuleConfig} from 'src/utils/modules.js'

const editorStore = useEditorStore()

const closePropertyDialog = () => {
  editorStore.closePropertyDialog()
}

const showProcessModelDialog = () => {
  editorStore.showProcessModelDialog(editorStore.propertyDialog.node)
}

const applyProperties = () => {
  // TODO: 实现应用属性
  console.log('应用属性:', editorStore.propertyDialog.data)
}

const applyAndCloseProperties = () => {
  applyProperties()
  closePropertyDialog()
}
</script>

<style lang="scss" scoped>
.property-dialog {
  width: 600px;
  max-width: 90vw;
  height: 500px;
  max-height: 80vh;

  .dialog-header {
    padding: 16px 24px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;

    .dialog-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .dialog-body {
    padding: 0;
    flex: 1;
    overflow: hidden;

    .property-editor {
      height: 100%;
      display: flex;
      flex-direction: column;

      .property-tabs {
        background: #fafafa;
        border-bottom: 1px solid #e0e0e0;
      }

      .property-content {
        flex: 1;
        overflow-y: auto;

        .tab-pane {
          padding: 16px;

          .property-form {
            .form-field {
              margin-bottom: 16px;
            }

            .model-description {
              color: #666;
              font-style: italic;
              padding: 16px;
              background: #f9f9f9;
              border-radius: 4px;
              text-align: center;
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    padding: 16px 24px;
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
  }
}
</style>

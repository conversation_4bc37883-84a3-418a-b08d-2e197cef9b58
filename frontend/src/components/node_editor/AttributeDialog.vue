<template>
  <q-dialog
    v-model="editorStore.propertyDialog.visible"
    persistent
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
  >
    <q-card class="property-dialog">
      <q-card-section class="dialog-header">
        <div class="dialog-title">
          <q-icon name="settings" />
          <span>{{ editorStore.propertyDialog.title }}</span>
        </div>
        <q-btn
          flat
          dense
          round
          icon="close"
          @click="closePropertyDialog"
        />
      </q-card-section>

      <q-card-section class="dialog-body">
        <div class="property-editor">
          <!-- 属性标签页 -->
          <q-tabs
            v-model="editorStore.propertyDialog.activeTab"
            dense
            class="property-tabs"
            active-color="primary"
            indicator-color="primary"
            align="left"
          >
            <q-tab
              v-for="tab in editorStore.propertyDialog.tabs"
              :key="tab.id"
              :name="tab.id"
              :icon="tab.icon"
              :label="tab.label"
            />
          </q-tabs>

          <q-separator />

          <!-- 标签页内容 -->
          <q-tab-panels
            v-model="editorStore.propertyDialog.activeTab"
            animated
            class="property-content"
          >
            <!-- 基本信息Tab -->
            <q-tab-panel name="general" class="tab-pane">
              <div class="property-form">
                <q-input
                  v-if="editorStore.propertyDialog.data.name !== undefined"
                  v-model="editorStore.propertyDialog.data.name"
                  label="模块名称"
                  outlined
                  dense
                  class="form-field"
                />

                <q-input
                  v-if="editorStore.propertyDialog.data.type"
                  :model-value="getModuleTypeName(editorStore.propertyDialog.data.type)"
                  label="模块类型"
                  outlined
                  dense
                  readonly
                  class="form-field"
                />

                <div
                  v-if="ModuleConfig[editorStore.propertyDialog.data.type]?.supportProcessModel"
                  class="form-field"
                >
                  <q-input
                    :model-value="editorStore.propertyDialog.data.model || '无'"
                    label="进程模型"
                    outlined
                    dense
                    readonly
                  >
                    <template v-slot:append>
                      <q-btn
                        flat
                        dense
                        label="选择..."
                        @click="showProcessModelDialog"
                      />
                    </template>
                  </q-input>
                </div>
              </div>
            </q-tab-panel>

            <!-- 属性配置Tab -->
            <q-tab-panel name="attributes" class="tab-pane">
              <div class="property-form">
                <div
                  v-if="!editorStore.propertyDialog.data.attributeValues || Object.keys(editorStore.propertyDialog.data.attributeValues).length === 0"
                  class="model-description"
                >
                  此模块暂无可配置属性。选择进程模型后将显示相关属性。
                </div>
                <div v-else>
                  <div
                    v-for="(value, key) in editorStore.propertyDialog.data.attributeValues"
                    :key="key"
                    v-show="!key.startsWith('_')"
                    class="form-field"
                  >
                    <q-input
                      v-if="typeof value !== 'boolean'"
                      v-model="editorStore.propertyDialog.data.attributeValues[key]"
                      :label="key"
                      :type="typeof value === 'number' ? 'number' : 'text'"
                      outlined
                      dense
                    />
                    <q-checkbox
                      v-else
                      v-model="editorStore.propertyDialog.data.attributeValues[key]"
                      :label="key"
                    />
                  </div>
                </div>
              </div>
            </q-tab-panel>
          </q-tab-panels>
        </div>
      </q-card-section>

      <q-card-actions class="dialog-footer" align="right">
        <q-btn
          flat
          label="取消"
          @click="closePropertyDialog"
        />
        <q-btn
          flat
          label="应用"
          color="primary"
          @click="applyProperties"
        />
        <q-btn
          unelevated
          label="确定"
          color="primary"
          @click="applyAndCloseProperties"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useEditorStore } from 'stores/editor'
import {getModuleTypeName, ModuleConfig} from 'src/utils/modules.js'

const editorStore = useEditorStore()

const closePropertyDialog = () => {
  editorStore.closePropertyDialog()
}

const showProcessModelDialog = () => {
  editorStore.showProcessModelDialog(editorStore.propertyDialog.node)
}

const applyProperties = () => {
  // TODO: 实现应用属性
  console.log('应用属性:', editorStore.propertyDialog.data)
}

const applyAndCloseProperties = () => {
  applyProperties()
  closePropertyDialog()
}
</script>

<style lang="scss" scoped>
.property-dialog {
  width: 600px;
  max-width: 90vw;
  height: 500px;
  max-height: 80vh;

  .dialog-header {
    padding: 16px 24px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;

    .dialog-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .dialog-body {
    padding: 0;
    flex: 1;
    overflow: hidden;

    .property-editor {
      height: 100%;
      display: flex;
      flex-direction: column;

      .property-tabs {
        background: #fafafa;
        border-bottom: 1px solid #e0e0e0;
      }

      .property-content {
        flex: 1;
        overflow-y: auto;

        .tab-pane {
          padding: 16px;

          .property-form {
            .form-field {
              margin-bottom: 16px;
            }

            .model-description {
              color: #666;
              font-style: italic;
              padding: 16px;
              background: #f9f9f9;
              border-radius: 4px;
              text-align: center;
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    padding: 16px 24px;
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
  }
}
</style>

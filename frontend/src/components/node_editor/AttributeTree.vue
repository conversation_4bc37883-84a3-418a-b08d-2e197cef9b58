<template>
  <div class="attribute-tree">
    <div v-if="!attributes || attributes.length === 0" class="empty-state">
      <q-icon name="info" size="24px" color="grey-5" />
      <div class="empty-text">
        {{ emptyMessage }}
      </div>
    </div>
    
    <div v-else class="tree-container">
      <q-tree
        :nodes="treeNodes"
        node-key="id"
        default-expand-all
        no-connectors
        class="attribute-tree-view"
      >
        <template v-slot:default-header="prop">
          <div class="tree-node-header">
            <q-icon 
              :name="getNodeIcon(prop.node)" 
              :color="getNodeColor(prop.node)"
              size="16px"
              class="node-icon"
            />
            <span class="node-label">{{ prop.node.label }}</span>
            <q-space />
            <div class="node-actions">
              <q-btn
                v-if="prop.node.type === 'array'"
                flat
                dense
                round
                size="sm"
                icon="add"
                color="primary"
                @click="addArrayItem(prop.node)"
              >
                <q-tooltip>添加项目</q-tooltip>
              </q-btn>
              <q-btn
                v-if="prop.node.removable"
                flat
                dense
                round
                size="sm"
                icon="remove"
                color="negative"
                @click="removeItem(prop.node)"
              >
                <q-tooltip>删除</q-tooltip>
              </q-btn>
            </div>
          </div>
        </template>
        
        <template v-slot:default-body="prop">
          <div class="tree-node-body">
            <!-- 基本类型属性编辑器 -->
            <div v-if="!prop.node.children" class="attribute-editor">
              <!-- 布尔类型 -->
              <q-toggle
                v-if="prop.node.type === 'bool'"
                v-model="prop.node.value"
                :label="prop.node.value ? '启用' : '禁用'"
                @update:model-value="updateValue(prop.node, $event)"
              />
              
              <!-- 数字类型 -->
              <q-input
                v-else-if="prop.node.type === 'int' || prop.node.type === 'float'"
                v-model.number="prop.node.value"
                :type="'number'"
                :step="prop.node.type === 'float' ? '0.01' : '1'"
                outlined
                dense
                :suffix="prop.node.unit"
                @update:model-value="updateValue(prop.node, $event)"
              />
              
              <!-- 字符串类型 -->
              <q-input
                v-else-if="prop.node.type === 'string'"
                v-model="prop.node.value"
                outlined
                dense
                @update:model-value="updateValue(prop.node, $event)"
              />
              
              <!-- 选择类型（有symbolMap的属性） -->
              <q-select
                v-else-if="prop.node.symbolMap && prop.node.symbolMap.length > 0"
                v-model="prop.node.value"
                :options="getSelectOptions(prop.node)"
                outlined
                dense
                emit-value
                map-options
                :clearable="prop.node.allowOtherValues"
                @update:model-value="updateValue(prop.node, $event)"
              />
              
              <!-- 描述信息 -->
              <div v-if="prop.node.desc" class="attribute-desc">
                {{ prop.node.desc }}
              </div>
            </div>
          </div>
        </template>
      </q-tree>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'

const props = defineProps({
  attributes: {
    type: Array,
    default: () => []
  },
  modelValue: {
    type: Object,
    default: () => ({})
  },
  emptyMessage: {
    type: String,
    default: '暂无可配置属性'
  }
})

const emit = defineEmits(['update:modelValue'])

// 生成树节点数据
const treeNodes = computed(() => {
  return buildTreeNodes(props.attributes, props.modelValue)
})

// 构建树节点
function buildTreeNodes(attributes, values, parentPath = '') {
  if (!attributes || !Array.isArray(attributes)) return []
  
  return attributes.map((attr, index) => {
    const path = parentPath ? `${parentPath}.${attr.name}` : attr.name
    const nodeId = `${path}_${index}`
    
    const node = {
      id: nodeId,
      name: attr.name,
      label: attr.label || attr.name,
      type: attr.type,
      desc: attr.desc,
      unit: attr.unit,
      value: values[attr.name] !== undefined ? values[attr.name] : attr.defaultValue,
      symbolMap: attr.symbolMap || [],
      allowOtherValues: attr.allowOtherValues,
      path: path,
      removable: false
    }
    
    // 处理数组类型
    if (attr.type === 'array') {
      const arrayValue = values[attr.name] || []
      node.children = []
      
      // 为数组中的每个项目创建子节点
      arrayValue.forEach((item, itemIndex) => {
        const itemNode = {
          id: `${nodeId}_item_${itemIndex}`,
          label: `项目 ${itemIndex + 1}`,
          type: 'object',
          removable: true,
          children: attr.children ? buildTreeNodes(attr.children, item, `${path}[${itemIndex}]`) : []
        }
        node.children.push(itemNode)
      })
    } else if (attr.children) {
      // 处理对象类型
      node.children = buildTreeNodes(attr.children, node.value || {}, path)
    }
    
    return node
  })
}

// 获取节点图标
function getNodeIcon(node) {
  switch (node.type) {
    case 'bool': return 'toggle_on'
    case 'int':
    case 'float': return 'numbers'
    case 'string': return 'text_fields'
    case 'array': return 'list'
    case 'object': return 'folder'
    default: return 'settings'
  }
}

// 获取节点颜色
function getNodeColor(node) {
  switch (node.type) {
    case 'bool': return 'green'
    case 'int':
    case 'float': return 'blue'
    case 'string': return 'orange'
    case 'array': return 'purple'
    case 'object': return 'grey'
    default: return 'grey'
  }
}

// 获取选择选项
function getSelectOptions(node) {
  if (!node.symbolMap || !Array.isArray(node.symbolMap)) return []
  
  return node.symbolMap.map(item => ({
    label: item.key,
    value: item.value
  }))
}

// 更新值
function updateValue(node, value) {
  const newValues = { ...props.modelValue }
  setNestedValue(newValues, node.path, value)
  emit('update:modelValue', newValues)
}

// 设置嵌套值
function setNestedValue(obj, path, value) {
  const keys = path.split('.')
  let current = obj
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    if (!(key in current)) {
      current[key] = {}
    }
    current = current[key]
  }
  
  current[keys[keys.length - 1]] = value
}

// 添加数组项目
function addArrayItem(node) {
  // TODO: 实现添加数组项目
  console.log('添加数组项目:', node)
}

// 删除项目
function removeItem(node) {
  // TODO: 实现删除项目
  console.log('删除项目:', node)
}
</script>

<style lang="scss" scoped>
.attribute-tree {
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #999;
    
    .empty-text {
      margin-top: 8px;
      font-size: 14px;
    }
  }
  
  .tree-container {
    .attribute-tree-view {
      .tree-node-header {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 4px 0;
        
        .node-icon {
          margin-right: 8px;
        }
        
        .node-label {
          font-weight: 500;
          font-size: 14px;
        }
        
        .node-actions {
          display: flex;
          gap: 4px;
        }
      }
      
      .tree-node-body {
        margin-top: 8px;
        margin-left: 24px;
        
        .attribute-editor {
          .attribute-desc {
            margin-top: 4px;
            font-size: 12px;
            color: #666;
            font-style: italic;
          }
        }
      }
    }
  }
}
</style>
